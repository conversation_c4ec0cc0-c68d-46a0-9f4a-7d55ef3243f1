import networkx as nx
import random
from collections import defaultdict

# === 第一步：读取关系网络文件 ===
def read_network(file_path):
    with open(file_path, 'r') as f:
        lines = f.readlines()
        n, m = map(int, lines[0].strip().split())
        edges = [tuple(map(int, line.strip().split())) for line in lines[1:]]
    G = nx.DiGraph()
    G.add_nodes_from(range(n))
    for u, v in edges:
        # 随机初始化传播概率，或自行设置权重
        G.add_edge(u, v, prob=random.uniform(0.1, 0.5))
    return G

# === 第二步：模拟IC模型下的传播路径 ===
def simulate_IC(G, seeds, max_steps=10):
    activated = set(seeds)
    newly_activated = set(seeds)
    propagation_path = []

    for step in range(max_steps):
        next_activated = set()
        for u in newly_activated:
            for v in G.successors(u):
                if v not in activated:
                    if random.random() < G[u][v]['prob']:
                        next_activated.add(v)
                        propagation_path.append((u, v))
        if not next_activated:
            break
        activated |= next_activated
        newly_activated = next_activated

    return activated, propagation_path

# === 第三步：分析导致黛玉被激活的关键路径 ===
def reverse_trace(G, target_node, trials=1000):
    count = defaultdict(int)
    path_freq = defaultdict(int)

    for _ in range(trials):
        seeds = [random.randint(0, G.number_of_nodes() - 1)]
        activated, path = simulate_IC(G, seeds)
        if target_node in activated:
            for u, v in path:
                count[u] += 1
                path_freq[(u, v)] += 1

    # 找出影响黛玉最多的人物（以频次衡量）
    sorted_nodes = sorted(count.items(), key=lambda x: x[1], reverse=True)
    sorted_paths = sorted(path_freq.items(), key=lambda x: x[1], reverse=True)
    return sorted_nodes, sorted_paths

# === 第四步：主程序 ===
def main():
    file_path = 'honglou.txt'  # 请确保文件放在当前目录
    G = read_network(file_path)

    dai_yu_id = 12  # 假设林黛玉的节点编号是 12（请根据实际编号调整）

    # 模拟反向传播分析
    node_contrib, path_contrib = reverse_trace(G, dai_yu_id, trials=1000)

    print("💡 影响黛玉的关键人物（按影响频次排序）：")
    for node, freq in node_contrib[:10]:
        print(f"人物节点 {node}: 频次 = {freq}")

    print("\n📌 导致黛玉悲剧的关键路径（频次最高的前10条）：")
    for (u, v), freq in path_contrib[:10]:
        print(f"{u} → {v}, 频次 = {freq}")

if __name__ == '__main__':
    main()
